import React from "react";
import { DonutChart } from "@/components/charts";
import { AnalyticsData } from "./types";
import { formatName } from "./utils";

interface AnalyticsSectionProps {
	title: string;
	data: AnalyticsData[];
	borderColor?: string;
	onItemClick?: (item: AnalyticsData) => void;
	enableShowTotalCta?: boolean;
}

export const AnalyticsSection = ({
	title,
	data,
	borderColor = "border-gray-200",
	onItemClick,
	enableShowTotalCta = false,
}: AnalyticsSectionProps) => (
	<div className="flex flex-col">
		{enableShowTotalCta && (
			<button className="text-accent text-sm font-semibold mt-2 underline underline-offset-[3px] hover:no-underline transition-all duration-200 w-fit mb-4">
				Show Total
			</button>
		)}
		<h3 className="text-sm font-semibold text-gray-600 mb-4">{title}</h3>
		<div className="space-y-2.5">
			{data.map((item, index) => (
				<div
					key={index}
					onClick={() => onItemClick?.(item)}
					className={`flex items-center justify-between py-2.5 px-5 bg-white border ${borderColor} rounded-lg shadow-sm ${
						onItemClick
							? "cursor-pointer hover:border-accent hover:shadow-md transition-all duration-200 hover:bg-purple-50"
							: ""
					}`}
				>
					<div className="flex flex-col">
						<span className="text-sm font-semibold text-gray-600 mb-1">
							{formatName(item.name)}
						</span>
						<div className="flex items-baseline gap-1">
							<span className="text-3xl font-normal text-black">
								{item.total}
							</span>
							<span className="text-sm font-normal text-gray-400">total</span>
						</div>
					</div>
					<div
						className={`flex-shrink-0 ${onItemClick ? "hover:scale-105 transition-transform duration-200" : ""}`}
					>
						<DonutChart
							data={[
								{
									name: "Correct",
									value: item.correct.value,
									fill: item.correct.fill,
								},
								{
									name: "Wrong",
									value: item.wrong.value,
									fill: item.wrong.fill,
								},
							]}
							showLegend={false}
							showTooltip={true}
							innerRadius={19}
							outerRadius={31}
							className="w-[63px] h-[63px]"
						/>
					</div>
				</div>
			))}
		</div>
	</div>
);
