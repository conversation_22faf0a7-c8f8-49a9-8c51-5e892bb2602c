import MobileSwitcher from "@/components/layout/main/mobile-switcher";
import { Navbar } from "@/components/layout/main/navbar";
import { useMediaQuery } from "react-responsive";
import { createFileRoute } from "@tanstack/react-router";
import { <PERSON><PERSON><PERSON> } from "@/components/charts";
import { useGetAnalytics } from "@/lib/queries/user.query";
import { AnalyticsEmptyState } from "@/components/common";
import { useState } from "react";
import { Button } from "@/components/ui/button";

import { Star } from "react-feather";

// Import analytics components
import {
	AnalyticsCard,
	AnalyticsSection,
	TestDetailSection,
	SubjectDetailModal,
	FuturePredictionComingSoon,
	AnalyticsMainLoading,
	AnalyticsFutureLoading,
	// Types and utilities
	TimeFrame,
	TimeFrameData,
	CHART_COLORS,
	TIME_FRAME_OPTIONS,
	transformDataToAnalytics,
	formatName,
} from "@/components/layout/analytics";

const Page = () => {
	const isDesktop = useMediaQuery({ minWidth: 1024 });

	// State for modal and time frame selection
	const [selectedSubject, setSelectedSubject] = useState<string | null>(null);
	const [selectedTimeFrame, setSelectedTimeFrame] =
		useState<TimeFrame>("twentyFourHours");
	const [selectedTest, setSelectedTest] = useState<any | null>(null);

	// Fetch analytics data from API
	const {
		data: analyticsData,
		isLoading,
		error: analyticsError,
	} = useGetAnalytics();
	const analytics = analyticsData?.data?.data?.analytics;

	// Get current time frame data
	const getCurrentTimeFrameData = (): TimeFrameData | undefined => {
		const timeFrameMap: Record<TimeFrame, any> = {
			twentyFourHours: analytics?.twentyFourHours,
			sevenDays: analytics?.sevenDays,
			thirtyDays: analytics?.thirtyDays,
			overall: analytics?.overall,
		};
		return timeFrameMap[selectedTimeFrame];
	};

	const currentData = getCurrentTimeFrameData();

	// Calculate analytics data based on API data
	const totalMCQsSolved = currentData?.mcqsSolvedCount || 0;
	const correctAnswers = currentData?.correctAnswers || 0;
	const wrongAnswers = currentData?.wrongAnswers || 0;

	// Determine if we should show empty state
	const hasAnalyticsData = !isLoading && !analyticsError && totalMCQsSolved > 0;

	// Get quiz name and AI analytics
	const quizName = analytics?.quiz_name || "NUST-Engineering";
	const aiAnalytic =
		analytics?.ai_based_analytic ||
		"You need to give some more quizzes in order to get helpful AI insights";
	const aiTopicAnalytics = analytics?.ai_topic_analytic || {};

	// Dummy subject feedback data
	const dummySubjectFeedback = [
		{
			subject: "Maths",
			aiRecommendation:
				"Your foundations are strong, you just need more practice in Trigonometry and Calculus.",
			weakTopics: ["Trigonometric Functions"],
			aiPrediction:
				"After covering weak topics, you will be able to achieve 85% accuracy in next test!",
		},
		{
			subject: "Physics",
			aiRecommendation:
				"Your foundations are strong, you just need more practice in Thermodynamics and Projectile Motion.",
			weakTopics: ["Thermodynamics"],
			aiPrediction:
				"After covering weak topics, you will be able to achieve 85% accuracy in next test!",
		},
		{
			subject: "Chemistry",
			aiRecommendation:
				"Your foundations are strong, you just need more practice in Inorganic Chemistry and Benzenes.",
			weakTopics: ["Benzenes"],
			aiPrediction:
				"After covering weak topics, you will be able to achieve 85% accuracy in next test!",
		},
		{
			subject: "English",
			aiRecommendation:
				"Your foundations are strong, you just need more practice in Subject-Verb Agreement.",
			weakTopics: ["Subject-Verb Agreement"],
			aiPrediction:
				"After covering weak topics, you will be able to achieve 85% accuracy in next test!",
		},
	];

	// Dummy test data (since we don't have real data yet)
	const dummyTests = [
		{
			id: 1,
			testName: "NET Engineering Standard",
			date: "07/06/25 18:59",
			totalMarks: 200,
			obtainedMarks: 167,
			mcqsSolved: 15,
			correctAnswers: 12,
			wrongAnswers: 3,
			subjects: { Mathematics: 5, Physics: 5, English: 5 },
			mcqsType: { numerical: 4, theoretical: 7, cramming: 4 },
			difficultyType: { easy: 8, medium: 4, hard: 3 },
		},
		{
			id: 2,
			testName: "NET Engineering Standard",
			date: "06/06/25 14:30",
			totalMarks: 200,
			obtainedMarks: 145,
			mcqsSolved: 15,
			correctAnswers: 10,
			wrongAnswers: 5,
			subjects: { Mathematics: 5, Physics: 5, English: 5 },
			mcqsType: { numerical: 5, theoretical: 6, cramming: 4 },
			difficultyType: { easy: 6, medium: 5, hard: 4 },
		},
		{
			id: 3,
			testName: "NET Engineering Standard",
			date: "05/06/25 16:45",
			totalMarks: 200,
			obtainedMarks: 178,
			mcqsSolved: 15,
			correctAnswers: 13,
			wrongAnswers: 2,
			subjects: { Mathematics: 5, Physics: 5, English: 5 },
			mcqsType: { numerical: 6, theoretical: 5, cramming: 4 },
			difficultyType: { easy: 9, medium: 4, hard: 2 },
		},
		{
			id: 4,
			testName: "NET Engineering Standard",
			date: "04/06/25 11:20",
			totalMarks: 200,
			obtainedMarks: 134,
			mcqsSolved: 15,
			correctAnswers: 9,
			wrongAnswers: 6,
			subjects: { Mathematics: 5, Physics: 5, English: 5 },
			mcqsType: { numerical: 3, theoretical: 8, cramming: 4 },
			difficultyType: { easy: 5, medium: 6, hard: 4 },
		},
		{
			id: 5,
			testName: "NET Engineering Standard",
			date: "03/06/25 09:15",
			totalMarks: 200,
			obtainedMarks: 156,
			mcqsSolved: 15,
			correctAnswers: 11,
			wrongAnswers: 4,
			subjects: { Mathematics: 5, Physics: 5, English: 5 },
			mcqsType: { numerical: 4, theoretical: 7, cramming: 4 },
			difficultyType: { easy: 7, medium: 5, hard: 3 },
		},
		{
			id: 6,
			testName: "NET Engineering Standard",
			date: "02/06/25 13:40",
			totalMarks: 200,
			obtainedMarks: 189,
			mcqsSolved: 15,
			correctAnswers: 14,
			wrongAnswers: 1,
			subjects: { Mathematics: 5, Physics: 5, English: 5 },
			mcqsType: { numerical: 5, theoretical: 6, cramming: 4 },
			difficultyType: { easy: 10, medium: 3, hard: 2 },
		},
	];

	// Analytics data using real API data where possible
	const totalAttemptedData = [
		{
			name: "Correct Answers",
			value: correctAnswers,
			fill: "rgba(89, 54, 205, 1)",
		},
		{
			name: "Wrong Answers",
			value: wrongAnswers,
			fill: "rgba(89, 54, 205, 0.5)",
		},
	];

	// Transform analytics data using utility functions
	const mcqTypeData = transformDataToAnalytics(
		currentData?.mcqsType || {},
		currentData?.subject_division || {},
		"type"
	);

	const difficultyTypeData = transformDataToAnalytics(
		currentData?.difficultyType || {},
		currentData?.subject_division || {},
		"difficulty"
	);

	const subjectWiseData = Object.entries(currentData?.subjects || {}).map(
		([subject, value]) => {
			const subjectData = currentData?.subject_division?.[subject];
			const subjectCorrect = Object.values(
				subjectData?.difficulty || {}
			).reduce((sum: number, data: any) => sum + (data.correct || 0), 0);
			const subjectWrong = Object.values(subjectData?.difficulty || {}).reduce(
				(sum: number, data: any) => sum + (data.wrong || 0),
				0
			);

			return {
				name: subject,
				total: value as number,
				correct: { value: subjectCorrect, fill: CHART_COLORS.correct },
				wrong: { value: subjectWrong, fill: CHART_COLORS.wrong },
			};
		}
	);

	// Subject-wise data for progress bars
	const _subjectWiseData = Object.entries(currentData?.subjects || {}).map(
		([subject, value]) => ({
			name: subject,
			totalValue: 100,
			lightValue:
				totalMCQsSolved > 0 ? ((value as number) * 100) / totalMCQsSolved : 0,
		})
	);

	const isFuturePredictionComingSoon = true;

	// Future predictions with slight improvement over current performance
	const predictedCorrectAnswers = Math.round(correctAnswers * 1.1); // 10% improvement prediction
	const predictedWrongAnswers = Math.round(wrongAnswers * 0.9); // 10% reduction in wrong answers

	const futurePredictionData = [
		{
			name: "Correct Answers",
			value: predictedCorrectAnswers,
			fill: "rgba(89, 54, 205, 1)",
		},
		{
			name: "Wrong Answers",
			value: predictedWrongAnswers,
			fill: "rgba(89, 54, 205, 0.5)",
		},
	];

	const subjectWisePredictionData = _subjectWiseData.map((item) => ({
		name: item.name,
		lightValue: Math.min(Math.round(item.lightValue * 1.1), 95), // 10% improvement, capped at 95%
		totalValue: 100,
	}));

	return (
		<>
			{!isDesktop && (
				<>
					<Navbar />
					<MobileSwitcher />
				</>
			)}
			<main className="container mx-auto p-4 pb-20 lg:pb-4">
				{/* I'm preparing for section */}
				<div className="mb-7 flex items-center gap-2 md:gap-3 justify-center text-sm md:text-lg lg:text-xl font-normal">
					<span className="text-[#475569]">I'm preparing for:</span>
					<div className="border border-accent rounded-full p-1">
						<div className="bg-accent text-white p-2 md:px-2.5 lg:px-[15px] lg:py-2.5 rounded-full font-bold">
							{quizName}
						</div>
					</div>
					<button className="text-accent underline underline-offset-[4px] hover:no-underline transition-all duration-200">
						Change
					</button>
				</div>

				{/* Header */}
				<div className="mb-7">
					<h2 className="font-bold text-2xl text-[#202224]">Analytics</h2>
				</div>

				{/* AI Based Analytics Section */}
				{hasAnalyticsData && (
					<div>
						<h3 className="text-xl font-bold text-gray-800 mb-3 flex items-center gap-2 justify-center">
							<Star className="w-5 h-5 text-accent" />
							AI Based Analytics
						</h3>
						<div className="mb-9 p-5 bg-gradient-to-r from-purple-50 to-blue-50 rounded-3xl border border-purple-200">
							<div className="flex items-start gap-3 mb-2">
								<div className="flex-1">
									<p className="text-[#475569] text-lg md:text-xl font-semibold mb-4">
										{aiAnalytic}
									</p>

									{/* Subject-wise AI Topic Analytics */}
									{Object.keys(aiTopicAnalytics).length > 0 && (
										<div className="space-y-3">
											<h4 className="text-lg md:text-xl font-semibold text-gray-800 mb-3">
												Subject-wise AI Insights:
											</h4>
											<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
												{Object.entries(aiTopicAnalytics).map(
													([subject, insight]) => (
														<div
															key={subject}
															className="bg-white/70 backdrop-blur-sm rounded-lg p-4 border border-purple-100"
														>
															<h5 className="font-semibold text-gray-800 mb-2 flex items-center gap-2">
																<div className="w-2 h-2 bg-accent rounded-full"></div>
																{subject}
															</h5>
															<p className="text-sm text-gray-600 leading-relaxed">
																{insight}
															</p>
														</div>
													)
												)}
											</div>
										</div>
									)}
								</div>
							</div>
						</div>
					</div>
				)}

				{/* Overview Header */}
				<div className="mb-7">
					<h2 className="font-bold text-2xl text-[#202224]">Overview</h2>
				</div>

				{/* Analytics with Time Frame Tabs */}
				{isLoading ? (
					<AnalyticsMainLoading />
				) : analyticsError ? (
					<AnalyticsEmptyState variant="no-data" className="py-20" />
				) : !hasAnalyticsData ? (
					<AnalyticsEmptyState variant="no-tests-completed" />
				) : (
					<div className="mb-9 px-6 py-7 bg-white rounded-3xl border border-gray-300">
						{/* Time Frame Tabs */}
						<div className="mb-6 flex gap-2 justify-center">
							{TIME_FRAME_OPTIONS.map(({ key, label }) => (
								<Button
									key={key}
									onClick={() => setSelectedTimeFrame(key)}
									className={`px-3 py-2 h-8 md:px-4 md:h-10 rounded-full text-xs md:text-sm font-medium ${
										selectedTimeFrame === key
											? "bg-accent text-white"
											: "bg-gray-100 text-gray-600 hover:bg-gray-200"
									}`}
								>
									{label}
								</Button>
							))}
						</div>

						{/* Header */}
						<div className="mb-5">
							<h3 className="text-sm font-bold text-gray-400 mb-2.5">Report</h3>
							<p className="text-[32px] font-medium text-gray-700">
								{totalMCQsSolved}{" "}
								<span className="text-base font-medium text-gray-400">
									total
								</span>
							</p>
						</div>

						{/* Main Content Grid */}
						<div className="grid grid-cols-1 lg:grid-cols-4 gap-9 lg:justify-between items-end">
							<PieChart
								data={totalAttemptedData}
								showLegend={true}
								innerRadius={0}
								outerRadius={70}
								className="flex flex-col justify-between h-[90%]"
							/>
							<AnalyticsSection
								title="Subject Wise"
								data={subjectWiseData}
								onItemClick={(item) => setSelectedSubject(item.name)}
								enableShowTotalCta={true}
							/>
							<AnalyticsSection
								title="MCQ's Type"
								data={mcqTypeData}
								borderColor="border-[#D2D5DA]"
							/>
							<AnalyticsSection
								title="Difficulty Type"
								data={difficultyTypeData}
							/>
						</div>
					</div>
				)}

				{/* Tests Taken Section */}
				{!selectedTest ? (
					<div className="mb-9">
						<div className="mb-7">
							<h2 className="font-bold text-2xl text-[#202224]">Tests Taken</h2>
						</div>

						<div className="bg-white rounded-3xl border border-gray-300 overflow-hidden">
							{/* Table Header */}
							<div className="grid grid-cols-6 gap-4 px-6 py-4 bg-gray-50 border-b border-gray-200 text-sm font-semibold text-gray-600">
								<div>Sr</div>
								<div>Test Name</div>
								<div>Date</div>
								<div>Total Marks</div>
								<div>Obtained Marks</div>
								<div></div>
							</div>

							{/* Table Rows */}
							<div className="divide-y divide-gray-100">
								{dummyTests.map((test, index) => (
									<div
										key={test.id}
										className="grid grid-cols-6 gap-4 px-6 py-4 hover:bg-gray-50 transition-colors duration-200"
									>
										<div className="text-sm text-gray-600">
											{String(index + 1).padStart(2, "0")}
										</div>
										<div>
											<button className="text-accent text-left hover:underline text-sm font-medium">
												{test.testName}
											</button>
										</div>
										<div className="text-sm text-gray-600">{test.date}</div>
										<div className="text-sm text-gray-900 font-medium">
											{test.totalMarks}
										</div>
										<div className="text-sm text-gray-900 font-medium">
											{test.obtainedMarks}
										</div>
										<div>
											<button
												onClick={() => setSelectedTest(test)}
												className="text-accent text-left hover:underline text-sm"
											>
												Analytics
											</button>
										</div>
									</div>
								))}
							</div>
						</div>
					</div>
				) : (
					/* Test Detail View */
					<div className="mb-9">
						<div className="mb-7">
							<h2 className="font-bold text-2xl text-[#202224]">Tests Taken</h2>
						</div>

						<div className="px-6 py-7 bg-white rounded-3xl border border-gray-300">
							{/* Header with Back Button */}
							<div className="mb-5 flex items-center justify-between">
								<div className="flex items-center gap-4">
									<button
										onClick={() => setSelectedTest(null)}
										className="flex items-center gap-2 text-accent transition-colors"
									>
										<svg
											className="w-10 h-10"
											fill="none"
											stroke="currentColor"
											viewBox="0 0 24 24"
										>
											<path
												strokeLinecap="round"
												strokeLinejoin="round"
												strokeWidth={2}
												d="M15 19l-7-7 7-7"
											/>
										</svg>
									</button>
									<div>
										<h3 className="text-sm font-bold text-gray-400 mb-2.5">
											Report
										</h3>
										<p className="text-[32px] font-medium text-gray-700">
											{selectedTest.obtainedMarks}{" "}
											<span className="text-base font-medium text-gray-400">
												total
											</span>
										</p>
									</div>
								</div>
								<button className="text-accent text-xl underline underline-offset-[4px] hover:no-underline transition-all duration-200">
									Go to test
								</button>
							</div>

							{/* Main Content Grid */}
							<div className="grid grid-cols-1 lg:grid-cols-4 gap-9 lg:justify-between items-end">
								<PieChart
									data={[
										{
											name: "Correct Answers",
											value: selectedTest.correctAnswers,
											fill: CHART_COLORS.correct,
										},
										{
											name: "Wrong Answers",
											value: selectedTest.wrongAnswers,
											fill: CHART_COLORS.wrong,
										},
									]}
									showLegend={true}
									innerRadius={0}
									outerRadius={70}
									className="flex flex-col justify-between h-[90%]"
								/>
								<TestDetailSection
									title="Subject Wise"
									data={selectedTest.subjects}
									enableShowTotalCta={true}
								/>
								<TestDetailSection
									title="MCQ's Type"
									data={selectedTest.mcqsType}
									borderColor="border-[#D2D5DA]"
								/>
								<TestDetailSection
									title="Difficulty Type"
									data={selectedTest.difficultyType}
								/>
							</div>
						</div>
					</div>
				)}

				{/* Subject Wise Feedback Section */}
				<div className="mb-9">
					<div className="mb-7">
						<h2 className="font-bold text-2xl text-[#202224]">
							Subject Wise Feedback
						</h2>
					</div>

					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
						{dummySubjectFeedback.map((feedback, index) => (
							<div
								key={index}
								className="bg-white rounded-3xl border border-gray-300 p-6"
							>
								{/* Subject Header */}
								<div className="flex items-center gap-3 mb-4">
									<div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
										<div className="w-6 h-6 bg-gray-300 rounded-full"></div>
									</div>
									<h3 className="font-semibold text-lg text-gray-800">
										{feedback.subject}
									</h3>
								</div>

								{/* AI-Based Recommendation */}
								<div className="mb-4">
									<h4 className="text-sm font-semibold text-gray-600 mb-2">
										AI-Based Recommendation:
									</h4>
									<p className="text-sm text-accent font-bold leading-relaxed">
										"{feedback.aiRecommendation}"
									</p>
								</div>

								{/* Weak Topics */}
								<div className="mb-4">
									<h4 className="text-sm font-semibold text-gray-600 mb-2">
										Weak Topics:
									</h4>
									<ul className="space-y-1">
										{feedback.weakTopics.map((topic, topicIndex) => (
											<li
												key={topicIndex}
												className="text-sm text-gray-700 flex items-start gap-2"
											>
												<span className="text-gray-400">•</span>
												<span>{topic}</span>
											</li>
										))}
									</ul>
								</div>

								{/* AI-Based Prediction */}
								<div>
									<h4 className="text-sm font-semibold text-gray-600 mb-2">
										AI-Based Prediction:
									</h4>
									<p className="text-sm text-accent leading-relaxed">
										"{feedback.aiPrediction}"
									</p>
								</div>
							</div>
						))}
					</div>
				</div>

				{/* Future Predictions Heading (Always Visible) */}
				<div className="mb-7">
					<h2 className="font-bold text-2xl text-[#202224]">
						Future Predictions
					</h2>
				</div>

				{/* Future Predictions */}
				{isFuturePredictionComingSoon ? (
					<FuturePredictionComingSoon />
				) : isLoading ? (
					<div className="grid grid-cols-1 lg:grid-cols-2 gap-2.5">
						<AnalyticsFutureLoading type="left" />
						<AnalyticsFutureLoading type="right" />
					</div>
				) : analyticsError ? (
					<AnalyticsEmptyState variant="no-data" className="py-20" />
				) : !hasAnalyticsData ? (
					<AnalyticsEmptyState variant="no-tests-completed" layout="grid" />
				) : (
					<div className="grid grid-cols-1 lg:grid-cols-2 gap-2.5">
						<AnalyticsCard
							title="PREDICTED MCQ'S"
							value={predictedCorrectAnswers + predictedWrongAnswers}
							subtitle="predicted"
							className="flex flex-col"
						>
							<PieChart
								data={futurePredictionData}
								showLegend={true}
								innerRadius={0}
								outerRadius={70}
								className="h-full flex flex-col justify-between"
							/>
						</AnalyticsCard>

						<AnalyticsCard
							title="SUBJECT WISE PREDICTION"
							value={`${Math.round((correctAnswers / (correctAnswers + wrongAnswers)) * 110) || 75}%`}
							subtitle="predicted avg"
						>
							<div className="space-y-4">
								{subjectWisePredictionData.map((item, index) => (
									<div key={index} className="space-y-2">
										<div className="flex justify-between items-center">
											<span className="text-sm font-medium text-[#1A1C1E]">
												{formatName(item.name)}
											</span>
										</div>
										<div className="flex gap-0.5">
											<div
												className="h-4 bg-[#5936CD80] transition-all duration-300"
												style={{ width: `${item.lightValue}%` }}
											/>
											<div
												className="h-4 bg-accent transition-all duration-300"
												style={{
													width: `${item.totalValue - item.lightValue}%`,
												}}
											/>
										</div>
									</div>
								))}
							</div>
						</AnalyticsCard>
					</div>
				)}

				{/* Subject Detail Modal */}
				{selectedSubject &&
					currentData?.subject_division?.[selectedSubject] && (
						<SubjectDetailModal
							isOpen={!!selectedSubject}
							onClose={() => setSelectedSubject(null)}
							subjectName={selectedSubject}
							subjectData={currentData.subject_division[selectedSubject]}
						/>
					)}
			</main>
		</>
	);
};

export const Route = createFileRoute("/(app)/_mainlayout/analytics")({
	beforeLoad: () => {},
	component: Page,
});
